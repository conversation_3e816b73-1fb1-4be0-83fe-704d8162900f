@echo off
echo ========================================
echo FoodIQ Production Build Script
echo ========================================
echo.

echo Step 1: Installing EAS CLI...
call npm install -g eas-cli
if %errorlevel% neq 0 (
    echo ERROR: Failed to install EAS CLI
    pause
    exit /b 1
)

echo.
echo Step 2: Checking if logged in to Expo...
call eas whoami
if %errorlevel% neq 0 (
    echo You need to login to Expo first.
    echo Please run: eas login
    echo Username: sarvesh2025
    pause
    exit /b 1
)

echo.
echo Step 3: Configuring EAS Build...
call eas build:configure
if %errorlevel% neq 0 (
    echo ERROR: Failed to configure EAS build
    pause
    exit /b 1
)

echo.
echo Step 4: Starting Production Build...
echo This will take 10-30 minutes...
call eas build --platform android --profile production
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo Check your email for the download link.
echo ========================================
pause
