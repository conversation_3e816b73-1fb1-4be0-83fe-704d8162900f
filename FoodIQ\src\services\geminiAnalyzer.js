// Gemini-powered ingredient analysis service
import { GoogleGenerativeAI } from '@google/generative-ai';

const GEMINI_API_KEY = 'AIzaSyDOnx-KQG8QB89tWU-gmQgSJqvCUQd6Q3Q';
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

export const analyzeIngredientsWithGemini = async (ingredientText) => {
  try {
    console.log('Analyzing ingredients with Gemini API...');
    
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    const prompt = `
    You are a nutrition expert and food scientist. Analyze the following ingredient list and provide a comprehensive health assessment.

    Ingredient List: "${ingredientText}"

    Please provide your analysis in the following JSON format (return ONLY valid JSON, no additional text):

    {
      "overallScore": [number from 1-10, where 1 is very unhealthy and 10 is very healthy],
      "description": "[brief description of overall health rating]",
      "color": "[hex color code: #4CAF50 for good (8-10), #8BC34<PERSON> for fair (6-7), #FFC107 for poor (4-5), #FF9800 for bad (2-3), #F44336 for very bad (1)]",
      "totalIngredients": [number of ingredients found],
      "ingredients": [
        {
          "name": "[ingredient name]",
          "category": "[Good/Bad/Neutral]",
          "score": [number from 1-10],
          "reason": "[explanation of why this ingredient is good/bad/neutral for health]"
        }
      ],
      "healthTips": [
        {
          "type": "[success/warning/danger/info]",
          "title": "[tip title]",
          "message": "[tip message]"
        }
      ],
      "alternatives": {
        "[bad ingredient name]": [
          {
            "name": "[alternative ingredient]",
            "reason": "[why this is a better choice]"
          }
        ]
      },
      "consumptionAdvice": {
        "frequency": "[regularly/moderately/occasionally/rarely]",
        "reason": "[explanation for consumption recommendation]"
      }
    }

    Guidelines for scoring:
    - Natural, whole food ingredients: 8-10 points
    - Minimally processed ingredients: 6-7 points  
    - Highly processed but generally safe: 4-5 points
    - Artificial additives, preservatives: 2-3 points
    - Harmful chemicals, trans fats: 1 point

    Consider factors like:
    - Processing level
    - Artificial additives
    - Preservatives
    - Sugar content
    - Nutritional value
    - Potential health risks
    - Scientific research on ingredients
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('Gemini response:', text);
    
    // Clean up the response to ensure it's valid JSON
    let cleanedText = text.trim();
    
    // Remove any markdown formatting
    cleanedText = cleanedText.replace(/```json\n?/g, '').replace(/```\n?/g, '');
    
    // Parse the JSON response
    const analysis = JSON.parse(cleanedText);
    
    // Validate the response structure
    if (!analysis.overallScore || !analysis.ingredients || !Array.isArray(analysis.ingredients)) {
      throw new Error('Invalid response structure from Gemini');
    }
    
    // Ensure all required fields are present
    analysis.overallScore = Math.max(1, Math.min(10, analysis.overallScore));
    analysis.description = analysis.description || getScoreDescription(analysis.overallScore);
    analysis.color = analysis.color || getScoreColor(analysis.overallScore);
    analysis.totalIngredients = analysis.totalIngredients || analysis.ingredients.length;
    analysis.healthTips = analysis.healthTips || [];
    analysis.alternatives = analysis.alternatives || {};
    analysis.consumptionAdvice = analysis.consumptionAdvice || {
      frequency: 'occasionally',
      reason: 'Moderate consumption recommended'
    };
    
    console.log('Processed analysis:', analysis);
    return analysis;
    
  } catch (error) {
    console.error('Gemini Analysis Error:', error);
    
    // Fallback to local analysis if Gemini fails
    console.log('Falling back to local ingredient analysis...');
    const { analyzeIngredientList } = await import('./ingredientAnalyzer');
    const localAnalysis = analyzeIngredientList(ingredientText);
    
    // Add missing fields for compatibility
    return {
      ...localAnalysis,
      healthTips: [
        {
          type: 'info',
          title: 'Analysis Complete',
          message: 'Used local analysis due to API unavailability.'
        }
      ],
      alternatives: {},
      consumptionAdvice: {
        frequency: localAnalysis.overallScore >= 7 ? 'regularly' : 
                  localAnalysis.overallScore >= 5 ? 'moderately' : 
                  localAnalysis.overallScore >= 3 ? 'occasionally' : 'rarely',
        reason: 'Based on ingredient health scores'
      }
    };
  }
};

// Helper functions for fallback scenarios
const getScoreDescription = (score) => {
  if (score >= 8) return 'Excellent - Very healthy ingredients';
  if (score >= 7) return 'Good - Mostly healthy ingredients';
  if (score >= 6) return 'Fair - Some healthy ingredients';
  if (score >= 4) return 'Poor - Many processed ingredients';
  if (score >= 2) return 'Very Poor - Highly processed with additives';
  return 'Extremely Poor - Avoid these ingredients';
};

const getScoreColor = (score) => {
  if (score >= 8) return '#4CAF50'; // Green
  if (score >= 6) return '#8BC34A'; // Light Green
  if (score >= 4) return '#FFC107'; // Yellow
  if (score >= 2) return '#FF9800'; // Orange
  return '#F44336'; // Red
};

// Function to analyze ingredients with image
export const analyzeIngredientsFromImage = async (imageBase64) => {
  try {
    console.log('Analyzing ingredients directly from image with Gemini...');
    
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    const prompt = `
    You are a nutrition expert. Analyze this food product image and provide a health assessment of its ingredients.

    Please:
    1. First extract all visible ingredient text from the image
    2. Then analyze each ingredient for health impact
    3. Provide an overall health score from 1-10

    Return your analysis in this JSON format (ONLY valid JSON, no additional text):

    {
      "extractedText": "[all ingredient text found in the image]",
      "overallScore": [number from 1-10],
      "description": "[brief description of overall health rating]",
      "color": "[hex color: #4CAF50 for good, #8BC34A for fair, #FFC107 for poor, #FF9800 for bad, #F44336 for very bad]",
      "totalIngredients": [number of ingredients],
      "ingredients": [
        {
          "name": "[ingredient name]",
          "category": "[Good/Bad/Neutral]", 
          "score": [1-10],
          "reason": "[health explanation]"
        }
      ],
      "healthTips": [
        {
          "type": "[success/warning/danger/info]",
          "title": "[tip title]",
          "message": "[tip message]"
        }
      ],
      "consumptionAdvice": {
        "frequency": "[regularly/moderately/occasionally/rarely]",
        "reason": "[explanation]"
      }
    }
    `;

    const imagePart = {
      inlineData: {
        data: imageBase64,
        mimeType: "image/jpeg"
      }
    };

    const result = await model.generateContent([prompt, imagePart]);
    const response = await result.response;
    const text = response.text();
    
    console.log('Gemini image analysis response:', text);
    
    // Clean and parse JSON
    let cleanedText = text.trim();
    cleanedText = cleanedText.replace(/```json\n?/g, '').replace(/```\n?/g, '');
    
    const analysis = JSON.parse(cleanedText);
    
    // Validate and ensure required fields
    analysis.overallScore = Math.max(1, Math.min(10, analysis.overallScore));
    analysis.alternatives = {};
    
    return analysis;
    
  } catch (error) {
    console.error('Gemini Image Analysis Error:', error);
    throw error;
  }
};
