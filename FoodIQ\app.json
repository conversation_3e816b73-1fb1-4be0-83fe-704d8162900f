{"expo": {"name": "FoodIQ", "slug": "foodiq", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#4CAF50"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.sarveshgovardhanan.foodiq"}, "android": {"package": "com.sarveshgovardhanan.foodiq", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.RECORD_AUDIO"], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#4CAF50"}, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-camera", {"cameraPermission": "Allow FoodIQ to access your camera to scan ingredient labels and analyze food products."}], ["expo-image-picker", {"photosPermission": "Allow FoodIQ to access your photos to analyze ingredient labels from your gallery."}], ["expo-media-library", {"photosPermission": "Allow FoodIQ to save analysis results to your photo library.", "savePhotosPermission": "Allow FoodIQ to save analysis results to your photo library."}]], "extra": {"eas": {"projectId": "d8ead599-4ab3-4dbc-b3d5-b74e9c52455b"}}}}