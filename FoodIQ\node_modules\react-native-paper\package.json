{"name": "react-native-paper", "version": "5.14.5", "description": "Material design for React Native", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "types": "lib/typescript/index.d.ts", "files": ["src", "lib", "react-navigation", "babel.js"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/callstack/react-native-paper.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/callstack/react-native-paper/issues"}, "homepage": "https://callstack.github.io/react-native-paper", "keywords": ["android", "ios", "react native", "component library", "ui kit", "material design", "bootstrap"], "workspaces": ["example", "docs"], "scripts": {"typescript": "tsc --noEmit", "lint": "yarn lint-no-fix --fix", "lint-no-fix": "eslint --ext '.js,.ts,.tsx' .", "test": "jest", "prepack": "bob build && node ./scripts/generate-mappings.js", "release": "release-it", "docs": "yarn --cwd docs", "example": "yarn --cwd example"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "dependencies": {"@callstack/react-theme-provider": "^3.0.9", "color": "^3.1.2", "use-latest-callback": "^0.2.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/runtime": "^7.25.0", "@callstack/eslint-config": "^13.0.2", "@commitlint/config-conventional": "^8.3.4", "@react-native-vector-icons/material-design-icons": "^12.0.0", "@react-navigation/native": "^6.1.2", "@release-it/conventional-changelog": "^1.1.0", "@testing-library/jest-native": "^5.4.1", "@testing-library/react-native": "11.5.0", "@types/color": "^3.0.0", "@types/jest": "^29.2.1", "@types/node": "^13.1.0", "@types/react-dom": "^18.3.1", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "all-contributors-cli": "^6.24.0", "babel-cli": "^6.26.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^29.6.3", "babel-loader": "^8.2.3", "babel-test": "^0.1.1", "chalk": "^4.0.0", "commitlint": "^8.3.4", "conventional-changelog-cli": "^2.0.11", "dedent": "^0.7.0", "eslint": "8.31.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-local-rules": "^1.3.2", "glob": "^7.1.3", "husky": "^1.3.1", "jest": "^29.6.3", "jest-file-snapshot": "^0.3.2", "metro-react-native-babel-preset": "0.73.9", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.77.0", "react-native-builder-bob": "^0.21.3", "react-native-safe-area-context": "5.1.0", "react-test-renderer": "18.3.1", "release-it": "^13.4.0", "rimraf": "^3.0.2", "typescript": "5.0.4"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-safe-area-context": "*"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "yarn lint-no-fix && yarn typescript && yarn test"}}, "jest": {"preset": "react-native", "setupFiles": ["<rootDir>/testSetup.js"], "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "cacheDirectory": "./cache/jest", "testPathIgnorePatterns": ["\\.d\\.ts$"], "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"], "watchPathIgnorePatterns": ["__fixtures__\\/[^/]+\\/(output|error)\\.js"], "transformIgnorePatterns": ["node_modules/(?!(@react-navigation|@react-native|react-native(-.*)?)/)"]}, "greenkeeper": {"ignore": ["expo", "react", "react-dom", "react-native", "react-test-renderer", "@types/react-native", "@types/jest", "husky", "eslint"]}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "packageManager": "yarn@4.9.1"}