# FoodIQ - Build and Deployment Guide

## Prerequisites Setup

### 1. Install Required Tools
```bash
# Install Expo CLI globally
npm install -g @expo/cli

# Install EAS CLI globally
npm install -g eas-cli

# Verify installations
expo --version
eas --version
```

### 2. Create Expo Account
```bash
# Create account at expo.dev (if you don't have one)
# Then login
eas login
```

### 3. Initialize EAS in Project
```bash
cd FoodIQ
eas build:configure
```

---

## Build Configuration

### 1. Verify app.json Configuration
Ensure your `app.json` has the correct production settings:

```json
{
  "expo": {
    "name": "FoodIQ",
    "slug": "foodiq",
    "version": "1.0.0",
    "android": {
      "package": "com.foodiq.app",
      "versionCode": 1
    }
  }
}
```

### 2. Verify eas.json Configuration
Your `eas.json` should be configured for production builds:

```json
{
  "build": {
    "production": {
      "android": {
        "buildType": "aab"
      }
    }
  }
}
```

---

## Build Process

### 1. Pre-Build Checklist
```bash
# Check for any issues
expo doctor

# Update dependencies
npm update

# Test the app locally
npm start
```

### 2. Build for Production
```bash
# Build Android App Bundle (AAB) for Play Store
eas build --platform android --profile production

# Alternative: Build APK for testing
eas build --platform android --profile preview
```

### 3. Build Process Monitoring
- Build typically takes 10-30 minutes
- You'll receive email notification when complete
- Monitor progress at: https://expo.dev/accounts/[username]/projects/foodiq/builds

### 4. Download Build
```bash
# List recent builds
eas build:list

# Download specific build
eas build:download [build-id]
```

---

## Build Troubleshooting

### Common Issues and Solutions

#### 1. Build Fails - Dependency Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules
npm install

# Try building again
eas build --platform android --profile production
```

#### 2. Build Fails - Configuration Issues
```bash
# Validate configuration
eas build:configure

# Check for syntax errors in app.json
npx expo config --type public
```

#### 3. Build Fails - Memory Issues
```bash
# Use large resource class (if available in your plan)
eas build --platform android --profile production --resource-class large
```

#### 4. Permission Issues
Ensure all required permissions are in app.json:
```json
"android": {
  "permissions": [
    "android.permission.CAMERA",
    "android.permission.READ_EXTERNAL_STORAGE",
    "android.permission.INTERNET"
  ]
}
```

---

## Testing Builds

### 1. Internal Testing
```bash
# Build APK for internal testing
eas build --platform android --profile preview

# Install on device
adb install path/to/foodiq.apk
```

### 2. Test Checklist
- [ ] App launches successfully
- [ ] Camera permission request works
- [ ] Image capture functions
- [ ] OCR text extraction works
- [ ] Gemini API analysis functions
- [ ] Manual input works
- [ ] Navigation between screens
- [ ] Error handling works
- [ ] App doesn't crash

### 3. Performance Testing
- Test on different Android versions
- Test on various device sizes
- Test with poor network conditions
- Test camera functionality in different lighting
- Test with various ingredient label types

---

## Signing and Security

### 1. App Signing (Recommended: Google Play App Signing)
- Let Google manage your signing keys
- More secure and easier to manage
- Automatic key rotation
- Recovery options if keys are lost

### 2. Manual Signing (Advanced)
If you prefer to manage your own keys:
```bash
# Generate keystore
keytool -genkey -v -keystore foodiq-release-key.keystore -alias foodiq -keyalg RSA -keysize 2048 -validity 10000

# Configure in eas.json
{
  "build": {
    "production": {
      "android": {
        "buildType": "aab",
        "credentialsSource": "local"
      }
    }
  }
}
```

---

## Build Optimization

### 1. Reduce Bundle Size
```json
// In app.json
{
  "expo": {
    "assetBundlePatterns": [
      "assets/images/*",
      "assets/fonts/*"
    ]
  }
}
```

### 2. Enable Hermes (JavaScript Engine)
```json
// In app.json
{
  "expo": {
    "android": {
      "jsEngine": "hermes"
    }
  }
}
```

### 3. Optimize Images
- Compress images in assets folder
- Use appropriate formats (PNG for icons, JPG for photos)
- Remove unused assets

---

## Automated Build Scripts

### 1. Create Build Script
Create `scripts/build.sh`:
```bash
#!/bin/bash

echo "🚀 Starting FoodIQ production build..."

# Check if EAS is installed
if ! command -v eas &> /dev/null; then
    echo "❌ EAS CLI not found. Installing..."
    npm install -g eas-cli
fi

# Login check
echo "🔐 Checking EAS login..."
eas whoami

# Pre-build checks
echo "🔍 Running pre-build checks..."
expo doctor

# Start build
echo "🏗️ Starting production build..."
eas build --platform android --profile production --non-interactive

echo "✅ Build submitted! Check your email for completion notification."
echo "📱 Monitor progress at: https://expo.dev"
```

### 2. Make Script Executable
```bash
chmod +x scripts/build.sh
```

### 3. Run Build Script
```bash
./scripts/build.sh
```

---

## Version Management

### 1. Update Version for New Builds
```json
// In app.json - increment for each release
{
  "expo": {
    "version": "1.0.1",
    "android": {
      "versionCode": 2
    }
  }
}
```

### 2. Version Naming Convention
- **Major.Minor.Patch** (e.g., 1.0.0)
- **Major**: Breaking changes
- **Minor**: New features
- **Patch**: Bug fixes

### 3. Version Code Rules
- Must be integer
- Must increase with each release
- Cannot be reused
- Start with 1, increment by 1

---

## Build Variants

### 1. Development Build
```bash
# For development and debugging
eas build --platform android --profile development
```

### 2. Preview Build
```bash
# For internal testing (APK format)
eas build --platform android --profile preview
```

### 3. Production Build
```bash
# For Play Store submission (AAB format)
eas build --platform android --profile production
```

---

## Post-Build Steps

### 1. Download and Verify Build
```bash
# Download the AAB file
eas build:download [build-id]

# Verify file integrity
file foodiq-*.aab
```

### 2. Test AAB File
```bash
# Install using bundletool (optional)
bundletool build-apks --bundle=foodiq.aab --output=foodiq.apks
bundletool install-apks --apks=foodiq.apks
```

### 3. Prepare for Upload
- Ensure AAB file is downloaded
- Verify file size (should be reasonable)
- Keep build ID for reference
- Prepare release notes

---

## Continuous Integration (Optional)

### 1. GitHub Actions Example
Create `.github/workflows/build.yml`:
```yaml
name: EAS Build
on:
  push:
    branches: [main]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install
      - run: npx eas-cli build --platform android --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
```

### 2. Environment Variables
Set in your CI/CD platform:
- `EXPO_TOKEN`: Your Expo access token
- `ANDROID_KEYSTORE`: Base64 encoded keystore (if using manual signing)

---

## Build Monitoring and Logs

### 1. Check Build Status
```bash
# List all builds
eas build:list

# Get specific build details
eas build:view [build-id]
```

### 2. Build Logs
- Available in Expo dashboard
- Downloadable for debugging
- Include detailed error information

### 3. Build Notifications
- Email notifications for completion
- Webhook support for CI/CD integration
- Slack/Discord integration available

This guide covers the complete build and deployment process for your FoodIQ app using Expo's EAS Build service.
