# FoodIQ - Ingredient Health Scanner

FoodIQ is a React Native app built with Expo that helps users analyze the health impact of food ingredients by scanning product labels with their camera.

## Features

### Core Functionality
- **Camera Scanning**: Use your device's camera to capture ingredient labels
- **OCR Text Extraction**: Automatically extract text from ingredient labels using OCR technology
- **Health Analysis**: Get instant health ratings from 1-10 for food products
- **Ingredient Breakdown**: See detailed analysis of each ingredient with health categorization

### Ingredient Analysis
- **Health Categories**: Ingredients are classified as "Good", "Bad", or "Neutral"
- **Detailed Explanations**: Learn why each ingredient is classified in its category
- **Overall Health Score**: Get a comprehensive health rating for the entire product
- **Visual Indicators**: Color-coded system for easy understanding

### User Interface
- **Clean Design**: Modern, intuitive interface optimized for mobile devices
- **Camera Screen**: Easy-to-use camera interface with scanning frame
- **Results Screen**: Comprehensive display of health analysis and ingredient breakdown
- **Manual Input**: Fallback option to manually enter ingredients

### Additional Features
- **Scan History**: Automatically save scan results for future reference
- **Healthier Alternatives**: Get suggestions for healthier ingredient alternatives
- **Health Tips**: Receive personalized advice based on ingredient analysis
- **Product Recommendations**: Get consumption frequency recommendations

## Technical Implementation

### Technologies Used
- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and tools
- **Expo Camera**: Camera functionality for image capture
- **React Navigation**: Screen navigation and routing
- **React Native Paper**: Material Design UI components
- **AsyncStorage**: Local data persistence

### OCR Integration
The app includes a flexible OCR service that can be integrated with:
- Google Vision API
- AWS Textract
- Azure Computer Vision
- Tesseract.js (client-side OCR)

Currently uses a mock OCR service for demonstration purposes.

### Ingredient Database
- Comprehensive database of common food ingredients
- Health classifications based on nutritional science
- Scoring system from 1-10 for health impact
- Detailed explanations for each ingredient's health effects

## Installation and Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Mobile device with Expo Go app (for testing)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd FoodIQ
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (mobile)
   - Press 'w' for web version
   - Press 'a' for Android emulator
   - Press 'i' for iOS simulator

## Usage Instructions

### Scanning Ingredients

1. **Open the app** and tap "Start Scanning"
2. **Position the camera** over an ingredient label
3. **Tap the capture button** to take a photo
4. **Wait for analysis** - the app will extract text and analyze ingredients
5. **Review results** - see health score, ingredient breakdown, and recommendations

### Manual Input

1. **From the home screen**, tap "Enter ingredients manually"
2. **Type or paste** the ingredient list from a product label
3. **Tap "Analyze Ingredients"** to get health analysis
4. **Review results** same as camera scanning

### Understanding Results

- **Health Score**: 1-10 rating (1 = very unhealthy, 10 = very healthy)
- **Color Coding**: 
  - Green = Good ingredients
  - Yellow/Orange = Neutral ingredients
  - Red = Bad ingredients
- **Detailed Analysis**: Each ingredient shows its health impact and reasoning

## Configuration

### OCR Service Setup

To use a real OCR service instead of the mock implementation:

1. **Google Vision API**:
   - Get API key from Google Cloud Console
   - Replace `YOUR_API_KEY_HERE` in `src/services/ocrService.js`
   - Uncomment the Google Vision API implementation

2. **AWS Textract**:
   - Set up AWS credentials
   - Install AWS SDK
   - Implement the AWS Textract function

3. **Azure Computer Vision**:
   - Get API key from Azure portal
   - Install Azure SDK
   - Implement the Azure Vision function

### Customizing Ingredient Database

Edit `src/services/ingredientAnalyzer.js` to:
- Add new ingredients to the database
- Modify health scores and categories
- Update ingredient explanations
- Add new ingredient alternatives

## Project Structure

```
FoodIQ/
├── App.js                          # Main app component with navigation
├── src/
│   ├── screens/
│   │   ├── HomeScreen.js           # Welcome screen with app introduction
│   │   ├── CameraScreen.js         # Camera interface for scanning
│   │   └── ResultsScreen.js        # Analysis results display
│   ├── services/
│   │   ├── ocrService.js           # OCR text extraction service
│   │   ├── ingredientAnalyzer.js   # Ingredient analysis and scoring
│   │   ├── alternativesService.js  # Healthier alternatives suggestions
│   │   └── storageService.js       # Local data storage
│   └── utils/
│       └── errorHandler.js         # Error handling utilities
├── assets/                         # App icons and images
└── package.json                    # Dependencies and scripts
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Future Enhancements

- **Barcode Scanning**: Add UPC/barcode scanning for product lookup
- **Nutrition Database Integration**: Connect to comprehensive nutrition APIs
- **User Profiles**: Personalized recommendations based on dietary preferences
- **Social Features**: Share scans and recommendations with friends
- **Offline Mode**: Cache ingredient database for offline analysis
- **Multi-language Support**: Support for multiple languages
- **Advanced Filters**: Filter by dietary restrictions (vegan, gluten-free, etc.)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
