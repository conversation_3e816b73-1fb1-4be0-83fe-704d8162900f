#!/bin/bash

echo "========================================"
echo "FoodIQ Production Build Script"
echo "========================================"
echo

echo "Step 1: Installing EAS CLI..."
npm install -g eas-cli
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install EAS CLI"
    exit 1
fi

echo
echo "Step 2: Checking if logged in to Expo..."
eas whoami
if [ $? -ne 0 ]; then
    echo "You need to login to Expo first."
    echo "Please run: eas login"
    echo "Username: sarvesh2025"
    exit 1
fi

echo
echo "Step 3: Configuring EAS Build..."
eas build:configure
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to configure EAS build"
    exit 1
fi

echo
echo "Step 4: Starting Production Build..."
echo "This will take 10-30 minutes..."
eas build --platform android --profile production
if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo
echo "========================================"
echo "Build completed successfully!"
echo "Check your email for the download link."
echo "========================================"
