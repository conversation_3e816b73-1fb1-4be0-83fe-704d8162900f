# FoodIQ - Create Visual Assets

## Quick Asset Creation

### Option 1: Use Canva (Recommended - Easy)

1. **Go to [Canva.com](https://canva.com)**
2. **Create App Icon (512x512)**:
   - Search "app icon"
   - Choose a template
   - Change text to "FoodIQ"
   - Use green color (#4CAF50)
   - Add food/health icon (🥗 or 📱)
   - Download as PNG

3. **Create Feature Graphic (1024x500)**:
   - Search "app store feature graphic"
   - Add "FoodIQ" text
   - Add "Smart Food Scanner" subtitle
   - Use green theme
   - Download as PNG

### Option 2: Use Existing Assets (Quick)

I'll help you create basic assets using the existing ones:

**For App Icon**: 
- Use the current icon.png and resize to 512x512
- Make background solid green (#4CAF50)

**For Screenshots**:
- Take 4-6 screenshots of your app running
- Use your phone or emulator
- Show: Home screen, Camera, Results, Ingredient list

## Asset Requirements

### Required Files:
```
store-assets/
├── icon-512.png (512x512 - App icon)
├── feature-graphic.png (1024x500 - Feature graphic)
└── screenshots/
    ├── screenshot-1.png (Home screen)
    ├── screenshot-2.png (Camera screen)
    ├── screenshot-3.png (Results screen)
    └── screenshot-4.png (Ingredient breakdown)
```

### Screenshot Tips:
1. **Home Screen**: Show the welcome screen with "Start Scanning" button
2. **Camera Screen**: Show the camera interface with scanning frame
3. **Results Screen**: Show a health score (like 6/10) with color coding
4. **Ingredient List**: Show the detailed ingredient breakdown

## Quick Creation Steps:

### If you want me to help:
1. Run your app and take 4 screenshots
2. Send me the screenshots
3. I'll help you create the icon and feature graphic

### If you want to do it yourself:
1. Use Canva templates
2. Keep it simple with green theme
3. Focus on "FoodIQ" branding
4. Show the app's main function (scanning food)

## Asset Specifications:
- **App Icon**: 512x512 PNG, no transparency
- **Feature Graphic**: 1024x500 PNG/JPG
- **Screenshots**: 1080x1920 to 1080x2340 PNG/JPG

Let me know if you want me to create these for you or if you'll handle them!
