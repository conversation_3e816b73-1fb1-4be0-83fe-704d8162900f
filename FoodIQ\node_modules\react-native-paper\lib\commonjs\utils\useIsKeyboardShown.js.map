{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "useIsKeyboardShown", "onShow", "onHide", "useEffect", "willShowSubscription", "willHideSubscription", "didShowSubscription", "didHideSubscription", "Platform", "OS", "Keyboard", "addListener", "_willShowSubscription", "_willHideSubscription", "remove", "removeListener", "_didShowSubscription", "_didHideSubscription"], "sourceRoot": "../../../src", "sources": ["utils/useIsKeyboardShown.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAA2E,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAM5D,SAASkB,kBAAkBA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,EAAE;EACpEzB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIC,oBAAyD;IAC7D,IAAIC,oBAAyD;IAC7D,IAAIC,mBAAwD;IAC5D,IAAIC,mBAAwD;IAE5D,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzBL,oBAAoB,GAAGM,qBAAQ,CAACC,WAAW,CAAC,kBAAkB,EAAEV,MAAM,CAAC;MACvEI,oBAAoB,GAAGK,qBAAQ,CAACC,WAAW,CAAC,kBAAkB,EAAET,MAAM,CAAC;IACzE,CAAC,MAAM;MACLI,mBAAmB,GAAGI,qBAAQ,CAACC,WAAW,CAAC,iBAAiB,EAAEV,MAAM,CAAC;MACrEM,mBAAmB,GAAGG,qBAAQ,CAACC,WAAW,CAAC,iBAAiB,EAAET,MAAM,CAAC;IACvE;IAEA,OAAO,MAAM;MACX,IAAIM,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;QAAA,IAAAG,qBAAA,EAAAC,qBAAA;QACzB,KAAAD,qBAAA,GAAIR,oBAAoB,cAAAQ,qBAAA,eAApBA,qBAAA,CAAsBE,MAAM,EAAE;UAChCV,oBAAoB,CAACU,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL;UACAJ,qBAAQ,CAACK,cAAc,CAAC,kBAAkB,EAAEd,MAAM,CAAC;QACrD;QAEA,KAAAY,qBAAA,GAAIR,oBAAoB,cAAAQ,qBAAA,eAApBA,qBAAA,CAAsBC,MAAM,EAAE;UAChCT,oBAAoB,CAACS,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL;UACAJ,qBAAQ,CAACK,cAAc,CAAC,kBAAkB,EAAEb,MAAM,CAAC;QACrD;MACF,CAAC,MAAM;QAAA,IAAAc,oBAAA,EAAAC,oBAAA;QACL,KAAAD,oBAAA,GAAIV,mBAAmB,cAAAU,oBAAA,eAAnBA,oBAAA,CAAqBF,MAAM,EAAE;UAC/BR,mBAAmB,CAACQ,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL;UACAJ,qBAAQ,CAACK,cAAc,CAAC,iBAAiB,EAAEd,MAAM,CAAC;QACpD;QAEA,KAAAgB,oBAAA,GAAIV,mBAAmB,cAAAU,oBAAA,eAAnBA,oBAAA,CAAqBH,MAAM,EAAE;UAC/BP,mBAAmB,CAACO,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL;UACAJ,qBAAQ,CAACK,cAAc,CAAC,iBAAiB,EAAEb,MAAM,CAAC;QACpD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACA,MAAM,EAAED,MAAM,CAAC,CAAC;AACtB", "ignoreList": []}