# FoodIQ - App Store Asset Creation Guide

## Required Assets for Google Play Store

### 1. APP ICONS

#### High-Resolution Icon (Required)
- **Size**: 512x512 pixels
- **Format**: PNG (32-bit)
- **Background**: No transparency
- **File**: `icon-512.png`

#### Adaptive Icon (Required for Android)
- **Size**: 512x512 pixels
- **Format**: PNG (32-bit)
- **Safe Area**: 66dp radius circle (center 264x264 pixels)
- **File**: `adaptive-icon.png`

#### Icon Design Guidelines:
```
Design Elements:
- Primary Color: #4CAF50 (FoodIQ Green)
- Secondary Color: #45a049 (Darker Green)
- Accent Color: #FFFFFF (White)

Icon Concept Options:
1. 🥗 Salad bowl with scanning lines
2. 📱 Phone with food icon overlay
3. 🔍 Magnifying glass over ingredients list
4. ⚡ Lightning bolt with leaf (fast health analysis)
5. 📊 Health meter/gauge with food elements

Typography:
- Font: Bold, modern sans-serif
- Text: "FoodIQ" or just "FQ" for smaller sizes
```

### 2. FEATURE GRAPHIC

#### Specifications:
- **Size**: 1024x500 pixels
- **Format**: JPG or PNG
- **File**: `feature-graphic.png`

#### Content Ideas:
```
Layout Concept:
[Left Side - 40%]        [Right Side - 60%]
- FoodIQ Logo           - Phone mockup showing app
- Tagline               - "Scan. Analyze. Choose Better."
- Key features list     - Health score visualization

Text Elements:
- "FoodIQ" (Large, bold)
- "Smart Food Ingredient Scanner"
- "AI-Powered Health Analysis"
- "Make Informed Food Choices"

Visual Elements:
- Gradient background (#4CAF50 to #45a049)
- Phone mockup showing results screen
- Food icons (apple, carrot, etc.)
- Scanning animation lines
```

### 3. SCREENSHOTS

#### Phone Screenshots (Required - Minimum 2, Maximum 8)
- **Portrait**: 1080x1920 to 1080x2340 pixels
- **Landscape**: 1920x1080 to 2340x1080 pixels
- **Format**: PNG or JPG

#### Recommended Screenshot Sequence:

**Screenshot 1: Home Screen**
```
Content:
- FoodIQ welcome screen
- "Start Scanning" button prominent
- Clean, inviting interface
- Brief feature overview

Caption: "Welcome to FoodIQ - Your Smart Food Scanner"
```

**Screenshot 2: Camera Interface**
```
Content:
- Camera viewfinder active
- Scanning frame overlay
- Ingredient label in view
- Clear instructions

Caption: "Simply point your camera at ingredient labels"
```

**Screenshot 3: Analysis Results**
```
Content:
- Health score prominently displayed (e.g., 6/10)
- Color-coded rating
- "Fair - Some healthy ingredients" description
- Ingredient count

Caption: "Get instant AI-powered health analysis"
```

**Screenshot 4: Ingredient Breakdown**
```
Content:
- List of ingredients with individual scores
- Good/Bad/Neutral categories
- Detailed explanations for each ingredient
- Color-coded chips

Caption: "Understand every ingredient's health impact"
```

**Screenshot 5: Health Tips**
```
Content:
- Personalized health recommendations
- Warning icons for concerning ingredients
- Consumption advice
- Educational content

Caption: "Receive personalized health tips and advice"
```

**Screenshot 6: Healthier Alternatives**
```
Content:
- Suggestions for better ingredients
- Side-by-side comparisons
- "Instead of X, try Y" format
- Educational explanations

Caption: "Discover healthier alternatives for better choices"
```

#### Screenshot Design Guidelines:
```
Design Consistency:
- Use actual app interface (no mockups)
- Consistent status bar styling
- Real ingredient data examples
- Professional, clean presentation

Text Overlays (Optional):
- Highlight key features with arrows/callouts
- Use FoodIQ brand colors
- Keep text minimal and readable
- Ensure accessibility compliance

Sample Data:
- Use recognizable food products
- Show variety (healthy and unhealthy examples)
- Include popular ingredients
- Demonstrate different score ranges
```

### 4. TABLET SCREENSHOTS (Optional but Recommended)

#### 7-inch Tablet
- **Portrait**: 1200x1920 pixels
- **Landscape**: 1920x1200 pixels

#### 10-inch Tablet
- **Portrait**: 1600x2560 pixels
- **Landscape**: 2560x1600 pixels

### 5. ASSET CREATION TOOLS

#### Recommended Design Tools:
```
Free Options:
- Canva (templates available)
- GIMP (open source)
- Figma (free tier)
- Google Drawings

Paid Options:
- Adobe Photoshop
- Adobe Illustrator
- Sketch (Mac only)
- Affinity Designer

Online Generators:
- App Icon Generator (appicon.co)
- Adaptive Icon Generator
- Screenshot Framer tools
```

#### Asset Templates:

**Canva Template Search Terms:**
- "App icon design"
- "Mobile app screenshots"
- "Feature graphic template"
- "Health app design"

**Figma Community Resources:**
- Search for "app store assets"
- Look for "mobile app templates"
- Find "icon design systems"

### 6. ASSET ORGANIZATION

#### File Structure:
```
FoodIQ/
├── assets/
│   ├── icon.png (512x512 - for app.json)
│   ├── adaptive-icon.png (512x512 - for app.json)
│   ├── splash-icon.png (existing)
│   ├── favicon.png (existing)
│   └── store-assets/
│       ├── icon-512.png (high-res icon)
│       ├── feature-graphic.png (1024x500)
│       ├── screenshots/
│       │   ├── phone/
│       │   │   ├── 01-home-screen.png
│       │   │   ├── 02-camera-interface.png
│       │   │   ├── 03-analysis-results.png
│       │   │   ├── 04-ingredient-breakdown.png
│       │   │   ├── 05-health-tips.png
│       │   │   └── 06-alternatives.png
│       │   ├── tablet-7/
│       │   │   ├── 01-home-portrait.png
│       │   │   └── 02-results-landscape.png
│       │   └── tablet-10/
│       │       ├── 01-home-portrait.png
│       │       └── 02-results-landscape.png
│       └── source-files/
│           ├── icon-design.psd
│           ├── feature-graphic.psd
│           └── screenshot-templates.fig
```

### 7. QUALITY CHECKLIST

#### Before Submission:
```
Icon Checklist:
□ 512x512 pixels exactly
□ PNG format, 32-bit
□ No transparency in background
□ Readable at small sizes (48x48)
□ Follows Material Design guidelines
□ Consistent with app branding

Feature Graphic Checklist:
□ 1024x500 pixels exactly
□ High quality, no pixelation
□ Text is readable
□ Represents app functionality
□ Professional appearance
□ Brand consistent

Screenshot Checklist:
□ Correct dimensions for target devices
□ High resolution, crisp images
□ Real app interface (not mockups)
□ Demonstrates key features
□ Consistent UI styling
□ No personal information visible
□ Good lighting/contrast
□ Professional presentation
```

### 8. CONTENT GUIDELINES

#### What to Avoid:
- Medical claims or promises
- Copyrighted images or logos
- Personal information in screenshots
- Blurry or low-quality images
- Misleading functionality claims
- Inappropriate content

#### What to Include:
- Clear app functionality demonstration
- Professional, polished presentation
- Accurate representation of features
- Educational/informational focus
- User-friendly interface showcase
- Health and wellness themes

### 9. LOCALIZATION CONSIDERATIONS

If planning international release:
- Create assets in multiple languages
- Consider cultural food preferences
- Adapt color schemes if needed
- Translate screenshot text overlays
- Ensure icon works globally

### 10. ASSET TESTING

#### Before Final Submission:
1. **View at Different Sizes**: Test icon at various resolutions
2. **Device Testing**: View screenshots on actual devices
3. **Accessibility Check**: Ensure sufficient color contrast
4. **Brand Consistency**: Verify all assets match brand guidelines
5. **Competitor Analysis**: Compare with similar apps in store

This guide will help you create professional, compliant assets for your Google Play Store listing!
