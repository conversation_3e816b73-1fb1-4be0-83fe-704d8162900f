import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
  TextInput,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Chip,
  Divider,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { extractTextFromImage } from '../services/ocrService';
import { analyzeIngredientsWithGemini, analyzeIngredientsFromImage } from '../services/geminiAnalyzer';
import { saveScanResult } from '../services/storageService';

const ResultsScreen = ({ route, navigation }) => {
  const { imageUri, imageBase64, manualMode } = route.params || {};
  
  const [isLoading, setIsLoading] = useState(!manualMode);
  const [extractedText, setExtractedText] = useState('');
  const [manualText, setManualText] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [error, setError] = useState(null);
  const [useDirectImageAnalysis, setUseDirectImageAnalysis] = useState(true);

  useEffect(() => {
    if (!manualMode && imageBase64) {
      processImage();
    }
  }, [imageBase64, manualMode]);

  const processImage = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let result;
      let text = '';

      if (useDirectImageAnalysis) {
        // Use Gemini to analyze image directly (OCR + Analysis in one step)
        try {
          result = await analyzeIngredientsFromImage(imageBase64);
          text = result.extractedText || '';
          setExtractedText(text);
        } catch (directError) {
          console.log('Direct image analysis failed, falling back to two-step process:', directError);
          setUseDirectImageAnalysis(false);
          // Fall through to two-step process
        }
      }

      if (!result) {
        // Two-step process: OCR first, then analysis
        text = await extractTextFromImage(imageBase64);
        setExtractedText(text);

        // Analyze the extracted ingredients with Gemini
        result = await analyzeIngredientsWithGemini(text);
      }

      setAnalysis(result);

      // Save to history
      try {
        await saveScanResult({
          extractedText: text,
          analysis: result,
          imageUri: imageUri || null,
        });
      } catch (saveError) {
        console.warn('Failed to save scan result:', saveError);
      }

    } catch (err) {
      setError(err.message);
      Alert.alert('Error', err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeManualInput = async () => {
    if (!manualText.trim()) {
      Alert.alert('Error', 'Please enter some ingredients to analyze');
      return;
    }

    try {
      setIsLoading(true);

      // Use Gemini to analyze manually entered ingredients
      const result = await analyzeIngredientsWithGemini(manualText);
      setAnalysis(result);
      setExtractedText(manualText);

    } catch (err) {
      setError(err.message);
      Alert.alert('Error', err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const renderScoreCircle = (score, color) => (
    <View style={[styles.scoreCircle, { borderColor: color }]}>
      <Text style={[styles.scoreText, { color }]}>{score}</Text>
      <Text style={styles.scoreLabel}>/ 10</Text>
    </View>
  );

  const renderIngredientItem = (ingredient, index) => {
    const getCategoryColor = (category) => {
      switch (category) {
        case 'Good': return '#4CAF50';
        case 'Bad': return '#F44336';
        default: return '#FF9800';
      }
    };

    return (
      <Card key={index} style={styles.ingredientCard}>
        <Card.Content>
          <View style={styles.ingredientHeader}>
            <Text style={styles.ingredientName}>{ingredient.name}</Text>
            <Chip
              style={[styles.categoryChip, { backgroundColor: getCategoryColor(ingredient.category) }]}
              textStyle={styles.categoryChipText}
            >
              {ingredient.category}
            </Chip>
          </View>
          <Text style={styles.ingredientReason}>{ingredient.reason}</Text>
          <View style={styles.ingredientScore}>
            <Text style={styles.scoreLabel}>Health Score: </Text>
            <Text style={[styles.scoreValue, { color: getCategoryColor(ingredient.category) }]}>
              {ingredient.score}/10
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const getTipColor = (type) => {
    switch (type) {
      case 'success': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'danger': return '#F44336';
      default: return '#2196F3';
    }
  };

  const getTipIcon = (type) => {
    switch (type) {
      case 'success': return 'checkmark-circle';
      case 'warning': return 'warning';
      case 'danger': return 'alert-circle';
      default: return 'information-circle';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Analyzing ingredients...</Text>
          <Text style={styles.loadingSubtext}>
            Extracting text and evaluating health impact
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (manualMode && !analysis) {
    return (
      <SafeAreaView style={styles.container}>
        <ScrollView style={styles.scrollView}>
          <Card style={styles.manualInputCard}>
            <Card.Content>
              <Title style={styles.cardTitle}>Enter Ingredients Manually</Title>
              <Paragraph style={styles.cardSubtitle}>
                Type or paste the ingredient list from the product label
              </Paragraph>
              
              <TextInput
                style={styles.textInput}
                multiline
                numberOfLines={6}
                placeholder="e.g., Water, Sugar, Natural Flavors, Citric Acid..."
                value={manualText}
                onChangeText={setManualText}
                textAlignVertical="top"
              />
              
              <Button
                mode="contained"
                onPress={analyzeManualInput}
                style={styles.analyzeButton}
                disabled={!manualText.trim()}
              >
                Analyze Ingredients
              </Button>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  if (error && !analysis) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={60} color="#F44336" />
          <Text style={styles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={() => navigation.goBack()}
            style={styles.retryButton}
          >
            Try Again
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {imageUri && (
          <Image source={{ uri: imageUri }} style={styles.capturedImage} />
        )}
        
        {analysis && (
          <>
            {/* Overall Score Card */}
            <Card style={styles.scoreCard}>
              <LinearGradient
                colors={[analysis.color, analysis.color + '80']}
                style={styles.scoreGradient}
              >
                <Card.Content>
                  <View style={styles.scoreHeader}>
                    {renderScoreCircle(analysis.overallScore, '#fff')}
                    <View style={styles.scoreInfo}>
                      <Title style={styles.scoreTitle}>Health Score</Title>
                      <Text style={styles.scoreDescription}>{analysis.description}</Text>
                      <Text style={styles.ingredientCount}>
                        {analysis.totalIngredients} ingredients analyzed
                      </Text>
                    </View>
                  </View>
                </Card.Content>
              </LinearGradient>
            </Card>

            {/* Extracted Text Card */}
            {extractedText && (
              <Card style={styles.textCard}>
                <Card.Content>
                  <Title style={styles.cardTitle}>Extracted Text</Title>
                  <Text style={styles.extractedText}>{extractedText}</Text>
                </Card.Content>
              </Card>
            )}

            {/* Ingredients Analysis */}
            <Card style={styles.analysisCard}>
              <Card.Content>
                <Title style={styles.cardTitle}>Ingredient Analysis</Title>
                <Paragraph style={styles.cardSubtitle}>
                  Detailed breakdown of each ingredient's health impact
                </Paragraph>
              </Card.Content>
            </Card>

            {analysis.ingredients.map((ingredient, index) =>
              renderIngredientItem(ingredient, index)
            )}

            {/* Health Tips */}
            {analysis.healthTips && analysis.healthTips.length > 0 && (
              <Card style={styles.tipsCard}>
                <Card.Content>
                  <Title style={styles.cardTitle}>Health Tips</Title>
                  {analysis.healthTips.map((tip, index) => (
                    <View key={index} style={styles.tipItem}>
                      <View style={[styles.tipIcon, { backgroundColor: getTipColor(tip.type) }]}>
                        <Ionicons
                          name={getTipIcon(tip.type)}
                          size={16}
                          color="#fff"
                        />
                      </View>
                      <View style={styles.tipContent}>
                        <Text style={styles.tipTitle}>{tip.title}</Text>
                        <Text style={styles.tipMessage}>{tip.message}</Text>
                      </View>
                    </View>
                  ))}
                </Card.Content>
              </Card>
            )}

            {/* Consumption Advice */}
            {analysis.consumptionAdvice && (
              <Card style={styles.adviceCard}>
                <Card.Content>
                  <Title style={styles.cardTitle}>Consumption Recommendation</Title>
                  <View style={styles.adviceContent}>
                    <Text style={styles.adviceFrequency}>
                      Consume: <Text style={styles.adviceFrequencyValue}>
                        {analysis.consumptionAdvice.frequency}
                      </Text>
                    </Text>
                    <Text style={styles.adviceReason}>
                      {analysis.consumptionAdvice.reason}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            )}

            {/* Healthier Alternatives */}
            {analysis.alternatives && Object.keys(analysis.alternatives).length > 0 && (
              <Card style={styles.alternativesCard}>
                <Card.Content>
                  <Title style={styles.cardTitle}>Healthier Alternatives</Title>
                  {Object.entries(analysis.alternatives).map(([ingredient, alts], index) => (
                    <View key={index} style={styles.alternativeGroup}>
                      <Text style={styles.alternativeIngredient}>
                        Instead of: {ingredient}
                      </Text>
                      {alts.map((alt, altIndex) => (
                        <View key={altIndex} style={styles.alternativeItem}>
                          <Text style={styles.alternativeName}>• {alt.name}</Text>
                          <Text style={styles.alternativeReason}>{alt.reason}</Text>
                        </View>
                      ))}
                    </View>
                  ))}
                </Card.Content>
              </Card>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Camera')}
                style={styles.actionButton}
              >
                Scan Another
              </Button>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Home')}
                style={styles.actionButton}
              >
                Back to Home
              </Button>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 20,
  },
  loadingSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
    marginVertical: 20,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
  },
  capturedImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  scoreCard: {
    marginBottom: 16,
    elevation: 4,
  },
  scoreGradient: {
    borderRadius: 8,
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    marginRight: 20,
  },
  scoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  scoreLabel: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
  },
  scoreInfo: {
    flex: 1,
  },
  scoreTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  scoreDescription: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
    marginTop: 4,
  },
  ingredientCount: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
    marginTop: 4,
  },
  textCard: {
    marginBottom: 16,
    elevation: 2,
  },
  extractedText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  analysisCard: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  cardSubtitle: {
    color: '#666',
    fontSize: 14,
  },
  ingredientCard: {
    marginBottom: 12,
    elevation: 2,
  },
  ingredientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ingredientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textTransform: 'capitalize',
  },
  categoryChip: {
    marginLeft: 8,
  },
  categoryChipText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  ingredientReason: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  ingredientScore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  manualInputCard: {
    marginBottom: 16,
    elevation: 4,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    marginVertical: 16,
    minHeight: 120,
  },
  analyzeButton: {
    backgroundColor: '#4CAF50',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    marginBottom: 20,
  },
  actionButton: {
    flex: 0.45,
  },
  tipsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  tipMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  adviceCard: {
    marginBottom: 16,
    elevation: 2,
  },
  adviceContent: {
    marginTop: 8,
  },
  adviceFrequency: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
  },
  adviceFrequencyValue: {
    fontWeight: 'bold',
    color: '#4CAF50',
    textTransform: 'capitalize',
  },
  adviceReason: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  alternativesCard: {
    marginBottom: 16,
    elevation: 2,
  },
  alternativeGroup: {
    marginBottom: 16,
  },
  alternativeIngredient: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 8,
  },
  alternativeItem: {
    marginLeft: 16,
    marginBottom: 8,
  },
  alternativeName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 2,
  },
  alternativeReason: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
});

export default ResultsScreen;
