# FoodIQ - Google Play Store Publishing Guide

## Complete Step-by-Step Guide for Publishing to Google Play Store

### Prerequisites
- Google Play Console developer account ($25 one-time fee)
- Expo CLI installed globally: `npm install -g @expo/cli`
- EAS CLI installed: `npm install -g eas-cli`
- Expo account (free at expo.dev)

---

## 1. BUILD CONFIGURATION

### Step 1.1: Update app.json Configuration

First, let's configure your app for production release:

```json
{
  "expo": {
    "name": "FoodIQ",
    "slug": "foodiq",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "splash": {
      "image": "./assets/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#4CAF50"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.yourcompany.foodiq"
    },
    "android": {
      "package": "com.yourcompany.foodiq",
      "versionCode": 1,
      "compileSdkVersion": 34,
      "targetSdkVersion": 34,
      "permissions": [
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.INTERNET",
        "android.permission.ACCESS_NETWORK_STATE"
      ],
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#4CAF50"
      },
      "playStoreUrl": "https://play.google.com/store/apps/details?id=com.yourcompany.foodiq"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    },
    "plugins": [
      [
        "expo-camera",
        {
          "cameraPermission": "Allow FoodIQ to access your camera to scan ingredient labels."
        }
      ],
      [
        "expo-image-picker",
        {
          "photosPermission": "Allow FoodIQ to access your photos to analyze ingredient labels."
        }
      ],
      [
        "expo-media-library",
        {
          "photosPermission": "Allow FoodIQ to save analysis results to your photo library.",
          "savePhotosPermission": "Allow FoodIQ to save analysis results to your photo library."
        }
      ]
    ],
    "extra": {
      "eas": {
        "projectId": "your-project-id-here"
      }
    }
  }
}
```

### Step 1.2: Create app.config.js (Alternative to app.json)

For more dynamic configuration, create `app.config.js`:

```javascript
export default {
  expo: {
    name: "FoodIQ",
    slug: "foodiq",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#4CAF50"
    },
    assetBundlePatterns: ["**/*"],
    android: {
      package: "com.yourcompany.foodiq",
      versionCode: 1,
      compileSdkVersion: 34,
      targetSdkVersion: 34,
      permissions: [
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.INTERNET",
        "android.permission.ACCESS_NETWORK_STATE"
      ],
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#4CAF50"
      }
    },
    plugins: [
      [
        "expo-camera",
        {
          cameraPermission: "Allow FoodIQ to access your camera to scan ingredient labels."
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "Allow FoodIQ to access your photos to analyze ingredient labels."
        }
      ]
    ],
    extra: {
      eas: {
        projectId: process.env.EXPO_PROJECT_ID || "your-project-id-here"
      }
    }
  }
};
```

### Step 1.3: Important Configuration Notes

**Package Name**: Choose a unique package name (e.g., `com.yourcompany.foodiq`)
**Version Management**: 
- `version`: User-facing version (1.0.0, 1.0.1, etc.)
- `versionCode`: Internal Android version number (increment for each release)

---

## 2. APP STORE ASSETS

### Step 2.1: Required Visual Assets

You need to create the following assets with exact specifications:

#### App Icon Requirements
- **High-res icon**: 512x512 px (PNG, no transparency)
- **Adaptive icon**: 512x512 px (PNG, safe area: 66dp radius)
- **Feature graphic**: 1024x500 px (JPG or PNG)

#### Screenshot Requirements
- **Phone screenshots**: Minimum 2, maximum 8
  - Portrait: 1080x1920 px to 1080x2340 px
  - Landscape: 1920x1080 px to 2340x1080 px
- **7-inch tablet screenshots**: Optional but recommended
  - Portrait: 1200x1920 px
  - Landscape: 1920x1200 px
- **10-inch tablet screenshots**: Optional
  - Portrait: 1600x2560 px
  - Landscape: 2560x1600 px

### Step 2.2: Asset Creation Guidelines

#### App Icon Design Tips:
- Use the FoodIQ green color scheme (#4CAF50)
- Include a food/health-related symbol (🥗, 📱, or custom icon)
- Ensure readability at small sizes
- Follow Material Design guidelines

#### Screenshot Content:
1. **Home screen** showing the app introduction
2. **Camera screen** with scanning interface
3. **Results screen** showing health analysis
4. **Ingredient breakdown** with detailed scores
5. **Health tips** and recommendations

### Step 2.3: Asset File Structure
```
FoodIQ/
├── assets/
│   ├── icon.png (512x512)
│   ├── adaptive-icon.png (512x512)
│   ├── splash-icon.png
│   ├── favicon.png
│   └── store-assets/
│       ├── feature-graphic.png (1024x500)
│       ├── screenshots/
│       │   ├── phone-1.png
│       │   ├── phone-2.png
│       │   ├── phone-3.png
│       │   └── phone-4.png
│       └── icon-512.png
```

---

## 3. BUILD PROCESS

### Step 3.1: Install and Configure EAS

```bash
# Install EAS CLI globally
npm install -g eas-cli

# Login to your Expo account
eas login

# Initialize EAS in your project
cd FoodIQ
eas build:configure
```

### Step 3.2: Configure eas.json

Create `eas.json` in your project root:

```json
{
  "cli": {
    "version": ">= 5.9.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "aab"
      }
    }
  },
  "submit": {
    "production": {
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "internal"
      }
    }
  }
}
```

### Step 3.3: Generate Production Build

```bash
# Build for production (creates AAB file)
eas build --platform android --profile production

# Alternative: Build APK for testing
eas build --platform android --profile preview
```

**Build Process Notes:**
- First build may take 15-30 minutes
- You'll receive an email when build completes
- Download the AAB file from the Expo dashboard

### Step 3.4: Local Testing (Optional)

```bash
# Install the APK on a connected Android device
adb install path/to/your-app.apk

# Or use Expo's internal distribution
eas build --platform android --profile preview
```

---

## 4. PLAY STORE LISTING

### Step 4.1: Access Google Play Console

1. Go to [Google Play Console](https://play.google.com/console)
2. Sign in with your developer account
3. Click "Create app"

### Step 4.2: Basic App Information

**App Details:**
- **App name**: FoodIQ
- **Default language**: English (United States)
- **App or game**: App
- **Free or paid**: Free

**Category and Tags:**
- **Category**: Health & Fitness
- **Tags**: health, nutrition, food, ingredients, scanner

### Step 4.3: Store Listing Content

#### App Description Template:

**Short description (80 characters):**
```
Scan food labels and get instant health ratings for ingredients
```

**Full description (4000 characters max):**
```
🥗 FoodIQ - Your Smart Food Ingredient Analyzer

Make informed food choices with FoodIQ! Simply scan ingredient labels with your camera and get instant health insights powered by AI.

✨ KEY FEATURES:
📸 Smart Camera Scanning - Point your camera at any ingredient label
🤖 AI-Powered Analysis - Advanced ingredient health assessment
📊 Health Scoring - Clear 1-10 rating system for every product
🏷️ Ingredient Breakdown - Detailed analysis of each component
💡 Health Tips - Personalized recommendations and advice
🔄 Healthier Alternatives - Suggestions for better ingredient choices
📱 Manual Input - Type ingredients when camera isn't available

🎯 PERFECT FOR:
• Health-conscious shoppers
• People with dietary restrictions
• Parents choosing family foods
• Fitness enthusiasts
• Anyone wanting to eat healthier

🔬 HOW IT WORKS:
1. Open FoodIQ and tap "Start Scanning"
2. Point your camera at an ingredient label
3. Get instant AI-powered health analysis
4. Learn about each ingredient's impact
5. Discover healthier alternatives

🛡️ PRIVACY & SECURITY:
• No personal data collection
• Secure AI processing
• Local data storage option
• Camera used only for scanning

Transform your grocery shopping experience with FoodIQ - because knowing what's in your food is the first step to better health!

Download now and start making smarter food choices today! 🌟
```

### Step 4.4: Content Rating

Complete the content rating questionnaire:
- **Target age group**: Everyone
- **Content type**: Educational/Informational
- **Interactive elements**: None
- **Data collection**: Minimal (camera access only)

### Step 4.5: Privacy Policy

**Required for apps that access camera/storage. Create a privacy policy including:**

```
Privacy Policy for FoodIQ

Data Collection:
- Camera access: Only for scanning ingredient labels
- Photo access: Only for selecting images to analyze
- No personal information collected
- No data shared with third parties

Data Usage:
- Images processed locally and via secure AI service
- Analysis results stored locally on device
- No user tracking or analytics

Contact: [<EMAIL>]
```

Host this on your website and provide the URL in Play Console.

---

## 5. RELEASE MANAGEMENT

### Step 5.1: Upload Your Build

1. **Navigate to Release Management**:
   - Go to "Release" → "Production"
   - Click "Create new release"

2. **Upload AAB File**:
   - Click "Upload" and select your AAB file
   - Wait for upload and processing

3. **Release Notes**:
```
🎉 Welcome to FoodIQ v1.0!

✨ Features:
• Smart ingredient label scanning
• AI-powered health analysis
• Detailed ingredient breakdown
• Health tips and recommendations
• Healthier alternatives suggestions

📱 Perfect for making informed food choices!

🔒 Privacy-focused with secure local processing
```

### Step 5.2: Release Tracks Strategy

**Recommended Release Process:**

1. **Internal Testing** (First):
   - Upload to internal track
   - Test with 5-10 internal testers
   - Duration: 1-2 weeks

2. **Closed Testing** (Alpha):
   - Limited external testers (20-100)
   - Gather feedback and fix issues
   - Duration: 2-3 weeks

3. **Open Testing** (Beta):
   - Public beta with larger audience
   - Final testing before production
   - Duration: 1-2 weeks

4. **Production Release**:
   - Full public release
   - Gradual rollout (start with 5%, increase to 100%)

### Step 5.3: Release Configuration

**Rollout Settings:**
- **Staged rollout**: Start with 5% of users
- **Release type**: Managed release
- **Countries**: Start with your country, expand globally

**App Signing:**
- Use Google Play App Signing (recommended)
- Google manages your signing key securely

---

## 6. POST-PUBLICATION

### Step 6.1: Monitoring and Analytics

**Google Play Console Analytics:**
- Monitor crash reports
- Track user acquisition
- Review performance metrics
- Monitor user ratings and reviews

**Key Metrics to Watch:**
- Install conversion rate
- Crash-free sessions
- Average rating
- User retention

### Step 6.2: Review Management

**Responding to Reviews:**
- Respond to negative reviews professionally
- Thank users for positive feedback
- Address technical issues promptly
- Use reviews to guide future updates

**Review Response Template:**
```
Thank you for using FoodIQ! We appreciate your feedback. 
If you're experiencing any issues, please contact us at 
[support-email] and we'll help resolve them quickly.
```

### Step 6.3: Update Management

**For Future Updates:**

1. **Increment Version Numbers**:
   ```json
   {
     "version": "1.0.1",
     "android": {
       "versionCode": 2
     }
   }
   ```

2. **Build New Version**:
   ```bash
   eas build --platform android --profile production
   ```

3. **Upload to Play Console**:
   - Create new release
   - Upload new AAB
   - Add release notes

### Step 6.4: Ongoing Maintenance

**Regular Tasks:**
- Monitor app performance weekly
- Respond to reviews within 24-48 hours
- Update app every 2-3 months
- Keep dependencies updated
- Monitor Google Play policy changes

---

## 7. FOODIQ-SPECIFIC CONSIDERATIONS

### Step 7.1: Camera Permission Handling

**Play Store Review Notes:**
- Clearly explain camera usage in description
- Include permission rationale in app
- Demonstrate camera functionality in screenshots

### Step 7.2: API Usage Disclosure

**Gemini AI Integration:**
- Mention AI-powered analysis in description
- Include data processing information in privacy policy
- Ensure compliance with Google's AI content policies

### Step 7.3: Potential Review Issues

**Common Rejection Reasons:**
1. **Insufficient app functionality** - Ensure robust fallback systems
2. **Privacy policy missing** - Required for camera access
3. **Misleading health claims** - Use disclaimers about medical advice
4. **Broken functionality** - Test thoroughly before submission

**Mitigation Strategies:**
- Include disclaimer: "Not intended as medical advice"
- Provide comprehensive testing documentation
- Ensure app works without internet connection
- Include clear privacy policy

### Step 7.4: Health App Compliance

**Important Disclaimers to Include:**
```
"FoodIQ provides educational information about food ingredients. 
This app is not intended to diagnose, treat, cure, or prevent any 
disease. Always consult with healthcare professionals for medical advice."
```

---

## 8. TIMELINE AND CHECKLIST

### Pre-Submission Checklist (1-2 weeks):
- [ ] Update app.json with production settings
- [ ] Create all required visual assets
- [ ] Set up EAS build configuration
- [ ] Generate production AAB file
- [ ] Create privacy policy and host online
- [ ] Prepare store listing content
- [ ] Test app thoroughly on multiple devices

### Submission Process (1-3 days):
- [ ] Create app in Play Console
- [ ] Upload AAB file
- [ ] Complete store listing
- [ ] Submit for review

### Review Process (1-7 days):
- [ ] Google reviews app (typically 1-3 days)
- [ ] Address any review feedback
- [ ] Resubmit if necessary

### Post-Launch (Ongoing):
- [ ] Monitor analytics and crashes
- [ ] Respond to user reviews
- [ ] Plan future updates
- [ ] Track user feedback

**Total Timeline: 2-4 weeks from start to public availability**

This comprehensive guide should help you successfully publish FoodIQ to the Google Play Store. Remember to test thoroughly and be patient with the review process!
