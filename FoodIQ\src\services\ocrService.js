// OCR Service using Gemini API for extracting text from images
import { GoogleGenerativeAI } from '@google/generative-ai';

const GEMINI_API_KEY = 'AIzaSyDOnx-KQG8QB89tWU-gmQgSJqvCUQd6Q3Q';
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

export const extractTextFromImage = async (imageBase64) => {
  try {
    console.log('Extracting text from image using Gemini API...');

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const prompt = `
    Please extract all text from this image, focusing specifically on ingredient lists.
    If this appears to be a food product label, please extract the complete ingredients list.
    Return only the raw text without any additional formatting or commentary.
    `;

    const imagePart = {
      inlineData: {
        data: imageBase64,
        mimeType: "image/jpeg"
      }
    };

    const result = await model.generateContent([prompt, imagePart]);
    const response = await result.response;
    const text = response.text();

    if (!text || text.trim().length === 0) {
      throw new Error('No text detected in image');
    }

    console.log('Extracted text:', text);
    return text.trim();

  } catch (error) {
    console.error('Gemini OCR Error:', error);

    // Fallback to mock service if Gemini fails
    console.log('Falling back to mock OCR service...');
    return await mockOCRService(imageBase64);
  }
};

// Mock OCR service for demonstration
const mockOCRService = async (imageBase64) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Return mock ingredient text that would typically be extracted from an image
  const mockTexts = [
    `INGREDIENTS: Water, Sugar, High Fructose Corn Syrup, Natural Flavors, Citric Acid, Sodium Benzoate (Preservative), Caramel Color, Caffeine, Phosphoric Acid, Red 40, Blue 1`,
    
    `Ingredients: Enriched Wheat Flour (Wheat Flour, Niacin, Reduced Iron, Thiamine Mononitrate, Riboflavin, Folic Acid), Sugar, Vegetable Oil (Palm Oil, Soybean Oil), High Fructose Corn Syrup, Cocoa Powder, Salt, Baking Soda, Artificial Flavor, Soy Lecithin`,
    
    `INGREDIENTS: Organic Quinoa, Organic Black Beans, Organic Sweet Corn, Organic Red Bell Peppers, Organic Cilantro, Organic Lime Juice, Sea Salt, Organic Cumin, Organic Garlic Powder`,
    
    `Ingredients: Whole Grain Oats, Almonds, Honey, Coconut Oil, Dried Cranberries (Cranberries, Sugar, Sunflower Oil), Pumpkin Seeds, Sunflower Seeds, Cinnamon, Vanilla Extract, Sea Salt`,
    
    `INGREDIENTS: Chicken Breast, Water, Contains 2% or less of: Salt, Sodium Phosphate, Modified Food Starch, Carrageenan, Natural Flavor, Potassium Chloride, Sodium Erythorbate, Sodium Nitrite`
  ];
  
  // Return a random mock text
  const randomIndex = Math.floor(Math.random() * mockTexts.length);
  return mockTexts[randomIndex];
};

// Alternative OCR services that could be integrated:

// AWS Textract implementation
export const extractTextWithAWSTextract = async (imageBase64) => {
  // Implementation would go here
  // Requires AWS SDK and proper configuration
};

// Azure Computer Vision implementation
export const extractTextWithAzureVision = async (imageBase64) => {
  // Implementation would go here
  // Requires Azure Cognitive Services SDK
};

// Tesseract.js implementation (client-side OCR)
export const extractTextWithTesseract = async (imageUri) => {
  // Implementation would go here
  // Requires react-native-tesseract-ocr or similar package
};
