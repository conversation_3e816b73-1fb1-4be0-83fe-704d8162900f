# FoodIQ - Google Play Console Upload Instructions

## Step-by-Step Upload Process

### 1. Create App in Google Play Console

1. **Go to [Google Play Console](https://play.google.com/console)**
2. **Click "Create app"**
3. **Fill in details**:
   - App name: `FoodIQ`
   - Default language: `English (United States)`
   - App or game: `App`
   - Free or paid: `Free`
4. **Check policy boxes and click "Create app"**

### 2. Complete App Content

#### Privacy Policy:
1. **Go to "App content" → "Privacy policy"**
2. **Enter URL**: `https://your-website.com/privacy` (you'll need to host the privacy policy)
3. **Click "Save"**

#### App Access:
1. **Go to "App content" → "App access"**
2. **Select**: "All functionality is available without special access"
3. **Click "Save"**

#### Ads:
1. **Go to "App content" → "Ads"**
2. **Select**: "No, my app does not contain ads"
3. **Click "Save"**

#### Content Rating:
1. **Go to "App content" → "Content rating"**
2. **Click "Start questionnaire"**
3. **Answer**:
   - Category: `Reference, Education, or Information`
   - Violence: `No` to all
   - Sexual content: `No` to all
   - Profanity: `No`
   - Controlled substances: `No` to all
   - Gambling: `No` to all
   - Interactive elements: `No` to all
4. **Submit**

#### Target Audience:
1. **Go to "App content" → "Target audience"**
2. **Select**: `18 and older` (primary)
3. **Click "Save"**

#### Data Safety:
1. **Go to "App content" → "Data safety"**
2. **Answer**: 
   - Does your app collect data? `Yes` (for camera access)
   - Photos and videos: `Collected`, `Processed ephemerally`, `Required`, `App functionality`
3. **Submit**

### 3. Create Store Listing

1. **Go to "Store presence" → "Main store listing"**
2. **Fill in**:
   - App name: `FoodIQ`
   - Short description: `Scan food labels and get instant AI-powered health ratings for ingredients`
   - Full description: [Copy from PLAY_STORE_CONTENT.md]
3. **Upload Graphics**:
   - App icon: Upload your 512x512 icon
   - Feature graphic: Upload your 1024x500 graphic
   - Phone screenshots: Upload 2-8 screenshots
4. **Categorization**:
   - App category: `Health & Fitness`
   - Tags: `health, nutrition, food, ingredients, scanner`
5. **Contact details**:
   - Email: `<EMAIL>`
6. **Click "Save"**

### 4. Upload Your App

1. **Go to "Release" → "Production"**
2. **Click "Create new release"**
3. **Upload AAB file**: Click "Upload" and select your AAB file
4. **Release notes**: [Copy from PLAY_STORE_CONTENT.md]
5. **Review and rollout**: Click "Review release"
6. **Submit**: Click "Start rollout to production"

### 5. What Happens Next

1. **Review Process**: Google reviews your app (1-7 days)
2. **Approval**: You'll get an email when approved
3. **Publication**: App goes live 2-3 hours after approval
4. **Monitoring**: Check Google Play Console for metrics

## Quick Checklist:

- [ ] App created in Play Console
- [ ] Privacy policy hosted and URL added
- [ ] All app content sections completed
- [ ] Store listing filled out
- [ ] Graphics uploaded (icon, feature graphic, screenshots)
- [ ] AAB file uploaded
- [ ] Release submitted for review

## Need Help?

**Common Issues**:
- **Privacy policy**: Must be hosted on a website (you can use GitHub Pages for free)
- **Graphics**: Must meet exact size requirements
- **AAB upload**: File must be under 150MB

**Contact**: If you get stuck, email me the specific error message and I'll help you fix it.

Your app is ready to submit! The hardest part is done. 🚀
