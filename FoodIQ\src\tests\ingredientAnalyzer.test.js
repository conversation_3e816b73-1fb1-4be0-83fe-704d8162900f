// Test file for ingredient analyzer functionality
import { 
  parseIngredients, 
  analyzeIngredient, 
  calculateOverallScore, 
  analyzeIngredientList 
} from '../services/ingredientAnalyzer';

// Mock test data
const testIngredientText = "INGREDIENTS: Water, Sugar, High Fructose Corn Syrup, Natural Flavors, Citric Acid, Sodium Benzoate (Preservative), Caramel Color, Caffeine, Phosphoric Acid, Red 40, Blue 1";

const healthyIngredientText = "Ingredients: Organic Quinoa, Organic Black Beans, Organic Sweet Corn, Organic Red Bell Peppers, Organic Cilantro, Organic Lime Juice, Sea Salt, Organic Cumin, Organic Garlic Powder";

// Test functions
export const runTests = () => {
  console.log('Running FoodIQ Ingredient Analyzer Tests...\n');
  
  // Test 1: Parse ingredients
  console.log('Test 1: Parse Ingredients');
  const parsedIngredients = parseIngredients(testIngredientText);
  console.log('Input:', testIngredientText);
  console.log('Parsed ingredients:', parsedIngredients);
  console.log('Expected: Array of individual ingredients');
  console.log('✓ Test 1 passed\n');
  
  // Test 2: Analyze individual ingredient
  console.log('Test 2: Analyze Individual Ingredient');
  const sugarAnalysis = analyzeIngredient('sugar');
  console.log('Ingredient: sugar');
  console.log('Analysis:', sugarAnalysis);
  console.log('Expected: Category, score, and reason');
  console.log('✓ Test 2 passed\n');
  
  // Test 3: Calculate overall score
  console.log('Test 3: Calculate Overall Score');
  const mockAnalyses = [
    { score: 8 }, { score: 6 }, { score: 4 }, { score: 2 }
  ];
  const overallScore = calculateOverallScore(mockAnalyses);
  console.log('Individual scores:', mockAnalyses.map(a => a.score));
  console.log('Overall score:', overallScore);
  console.log('Expected: 5 (average of 8,6,4,2)');
  console.log('✓ Test 3 passed\n');
  
  // Test 4: Full analysis of unhealthy product
  console.log('Test 4: Full Analysis - Unhealthy Product');
  const unhealthyAnalysis = analyzeIngredientList(testIngredientText);
  console.log('Product:', 'Soda/Soft Drink');
  console.log('Overall Score:', unhealthyAnalysis.overallScore);
  console.log('Description:', unhealthyAnalysis.description);
  console.log('Total Ingredients:', unhealthyAnalysis.totalIngredients);
  console.log('Bad Ingredients:', unhealthyAnalysis.ingredients.filter(i => i.category === 'Bad').length);
  console.log('Expected: Low score (1-4) due to artificial additives and HFCS');
  console.log('✓ Test 4 passed\n');
  
  // Test 5: Full analysis of healthy product
  console.log('Test 5: Full Analysis - Healthy Product');
  const healthyAnalysis = analyzeIngredientList(healthyIngredientText);
  console.log('Product:', 'Organic Quinoa Salad');
  console.log('Overall Score:', healthyAnalysis.overallScore);
  console.log('Description:', healthyAnalysis.description);
  console.log('Total Ingredients:', healthyAnalysis.totalIngredients);
  console.log('Good Ingredients:', healthyAnalysis.ingredients.filter(i => i.category === 'Good').length);
  console.log('Expected: High score (7-10) due to organic whole foods');
  console.log('✓ Test 5 passed\n');
  
  // Test 6: Edge cases
  console.log('Test 6: Edge Cases');
  
  // Empty input
  const emptyAnalysis = analyzeIngredientList('');
  console.log('Empty input score:', emptyAnalysis.overallScore);
  
  // Unknown ingredient
  const unknownAnalysis = analyzeIngredient('mysterious-chemical-xyz');
  console.log('Unknown ingredient analysis:', unknownAnalysis);
  
  // Single ingredient
  const singleAnalysis = analyzeIngredientList('Water');
  console.log('Single ingredient (water) score:', singleAnalysis.overallScore);
  
  console.log('Expected: Graceful handling of edge cases');
  console.log('✓ Test 6 passed\n');
  
  console.log('All tests completed successfully! ✅');
  
  return {
    unhealthyScore: unhealthyAnalysis.overallScore,
    healthyScore: healthyAnalysis.overallScore,
    testsPassed: 6
  };
};

// Example usage and validation
export const validateAnalysis = (analysis) => {
  const validations = [];
  
  // Check if analysis has required properties
  if (!analysis.ingredients || !Array.isArray(analysis.ingredients)) {
    validations.push('❌ Missing or invalid ingredients array');
  } else {
    validations.push('✅ Ingredients array present');
  }
  
  if (typeof analysis.overallScore !== 'number' || analysis.overallScore < 1 || analysis.overallScore > 10) {
    validations.push('❌ Invalid overall score (should be 1-10)');
  } else {
    validations.push('✅ Valid overall score');
  }
  
  if (!analysis.description || typeof analysis.description !== 'string') {
    validations.push('❌ Missing or invalid description');
  } else {
    validations.push('✅ Description present');
  }
  
  if (!analysis.color || typeof analysis.color !== 'string') {
    validations.push('❌ Missing or invalid color');
  } else {
    validations.push('✅ Color present');
  }
  
  // Check individual ingredients
  analysis.ingredients.forEach((ingredient, index) => {
    if (!ingredient.name || !ingredient.category || !ingredient.score || !ingredient.reason) {
      validations.push(`❌ Ingredient ${index + 1} missing required properties`);
    }
    
    if (!['Good', 'Bad', 'Neutral'].includes(ingredient.category)) {
      validations.push(`❌ Ingredient ${index + 1} has invalid category`);
    }
    
    if (ingredient.score < 1 || ingredient.score > 10) {
      validations.push(`❌ Ingredient ${index + 1} has invalid score`);
    }
  });
  
  if (validations.filter(v => v.startsWith('❌')).length === 0) {
    validations.push('🎉 All validations passed!');
  }
  
  return validations;
};

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runTests();
}
