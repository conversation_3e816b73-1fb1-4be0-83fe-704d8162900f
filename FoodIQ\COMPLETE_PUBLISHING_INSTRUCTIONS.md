# FoodIQ - Complete Publishing Instructions

## BEFORE YOU START - CHECKLIST

### Prerequisites:
- [ ] Google Play Console developer account ($25 paid)
- [ ] Expo account created at expo.dev
- [ ] Privacy policy hosted online (see Step 1 below)
- [ ] Visual assets ready (see Step 2 below)

---

## STEP 1: HOST YOUR PRIVACY POLICY (REQUIRED)

### Option A: GitHub Pages (Free & Easy)
1. Go to [GitHub.com](https://github.com) and create account if needed
2. Create new repository called "foodiq-privacy"
3. Upload the `privacy-policy.html` file from your FoodIQ folder
4. Go to Settings → Pages → Source: Deploy from branch → main
5. Your URL will be: `https://yourusername.github.io/foodiq-privacy/privacy-policy.html`

### Option B: Other Free Hosting
- Netlify.com
- Vercel.com
- Firebase Hosting

**IMPORTANT**: Save this URL - you'll need it for Play Console!

---

## STEP 2: CREATE VISUAL ASSETS

### Required Assets:
1. **App Icon**: 512x512 PNG (no transparency)
2. **Feature Graphic**: 1024x500 PNG/JPG
3. **Screenshots**: 2-8 phone screenshots (1080x1920 to 1080x2340)

### Quick Creation with Canva:
1. Go to [Canva.com](https://canva.com)
2. Search "app icon" → Create 512x512 icon with "FoodIQ" text and green theme
3. Search "app store feature graphic" → Create 1024x500 graphic
4. Take screenshots of your app running (Home, Camera, Results screens)

### Save Files As:
```
store-assets/
├── icon-512.png
├── feature-graphic.png
└── screenshots/
    ├── screenshot-1.png (Home screen)
    ├── screenshot-2.png (Camera screen)
    ├── screenshot-3.png (Results screen)
    └── screenshot-4.png (Ingredient breakdown)
```

---

## STEP 3: BUILD YOUR APP

### Commands to Run:
```bash
# 1. Open Command Prompt and navigate to FoodIQ folder
cd "c:\Users\<USER>\Documents\augment-projects\expo app foodiq\FoodIQ"

# 2. Install EAS CLI
npm install -g eas-cli

# 3. Login to Expo
eas login
# Username: sarvesh2025
# Password: [your password]

# 4. Configure build
eas build:configure

# 5. Build production AAB
eas build --platform android --profile production
```

### What Happens:
- Build takes 10-30 minutes
- You'll get email when complete
- Download the `.aab` file from the email link
- File will be named: `com.sarveshgovardhanan.foodiq-1.0.0.aab`

---

## STEP 4: GOOGLE PLAY CONSOLE SETUP

### 4.1: Create App
1. Go to [Google Play Console](https://play.google.com/console)
2. Click "Create app"
3. Fill in:
   - App name: `FoodIQ`
   - Default language: `English (United States)`
   - App or game: `App`
   - Free or paid: `Free`
4. Check policy boxes and click "Create app"

### 4.2: App Content (Complete ALL sections)

#### Privacy Policy:
- Go to "App content" → "Privacy policy"
- Enter your hosted privacy policy URL
- Click "Save"

#### App Access:
- Go to "App content" → "App access"
- Select "All functionality is available without special access"
- Click "Save"

#### Ads:
- Go to "App content" → "Ads"
- Select "No, my app does not contain ads"
- Click "Save"

#### Content Rating:
- Go to "App content" → "Content rating"
- Click "Start questionnaire"
- Answer ALL questions with "No" (app is educational, no violence/sexual content/etc.)
- Category: "Reference, Education, or Information"
- Submit

#### Target Audience:
- Go to "App content" → "Target audience"
- Select "18 and older" as primary
- Click "Save"

#### Data Safety:
- Go to "App content" → "Data safety"
- Answer: App collects "Photos and videos" for "App functionality"
- Mark as "Processed ephemerally" and "Required"
- Submit

### 4.3: Store Listing
- Go to "Store presence" → "Main store listing"

#### App Details:
```
App name: FoodIQ

Short description (80 chars):
Scan food labels and get instant AI-powered health ratings for ingredients

Full description:
🥗 FoodIQ - Your Smart Food Ingredient Analyzer

Make informed food choices with FoodIQ! Simply scan ingredient labels with your camera and get instant health insights powered by advanced AI technology.

✨ KEY FEATURES:
📸 Smart Camera Scanning - Point your camera at any ingredient label
🤖 AI-Powered Analysis - Advanced ingredient health assessment using Gemini AI
📊 Health Scoring - Clear 1-10 rating system for every product
🏷️ Ingredient Breakdown - Detailed analysis of each component
💡 Health Tips - Personalized recommendations and advice
🔄 Healthier Alternatives - Suggestions for better ingredient choices
📱 Manual Input - Type ingredients when camera isn't available

🎯 PERFECT FOR:
• Health-conscious shoppers
• People with dietary restrictions
• Parents choosing family foods
• Fitness enthusiasts
• Anyone wanting to eat healthier

🔬 HOW IT WORKS:
1. Open FoodIQ and tap "Start Scanning"
2. Point your camera at an ingredient label
3. Get instant AI-powered health analysis
4. Learn about each ingredient's impact
5. Discover healthier alternatives

🌟 WHY CHOOSE FOODIQ:
• Powered by Google's advanced Gemini AI
• Comprehensive ingredient database
• Science-based health assessments
• Easy-to-understand color-coded ratings
• Privacy-focused design

🛡️ PRIVACY & SECURITY:
• No personal data collection
• Secure AI processing
• Camera used only for scanning
• No tracking or advertising

Transform your grocery shopping experience with FoodIQ - because knowing what's in your food is the first step to better health!

Download now and start making smarter food choices today! 🌟

*Disclaimer: This app provides educational information only and is not intended as medical advice.*
```

#### Graphics:
- Upload app icon (512x512)
- Upload feature graphic (1024x500)
- Upload 2-8 phone screenshots

#### Categorization:
- App category: `Health & Fitness`
- Tags: `health, nutrition, food, ingredients, scanner`

#### Contact Details:
- Email: `<EMAIL>`

---

## STEP 5: UPLOAD APP BUNDLE

### 5.1: App Signing
- Go to "Release" → "Setup" → "App signing"
- Choose "Use Google-generated key" (recommended)
- Accept terms and save

### 5.2: Create Release
- Go to "Release" → "Production"
- Click "Create new release"

### 5.3: Upload AAB
- Click "Upload" in "App bundles" section
- Select your `.aab` file
- Wait for processing

### 5.4: Release Notes:
```
🎉 Welcome to FoodIQ v1.0!

✨ Features:
• Smart ingredient label scanning
• AI-powered health analysis
• Detailed ingredient breakdown
• Health tips and recommendations
• Healthier alternatives suggestions

📱 Perfect for making informed food choices!

🔒 Privacy-focused with secure local processing

Thank you for choosing FoodIQ! Rate us 5 stars if you love the app! ⭐
```

### 5.5: Submit
- Click "Review release"
- Click "Start rollout to production"

---

## STEP 6: REVIEW PROCESS

### Timeline:
- Review takes 1-7 days (usually 1-3 days)
- You'll get email notification
- App goes live 2-3 hours after approval

### Common Rejection Reasons:
- Missing privacy policy
- Broken app functionality
- Misleading descriptions
- Missing required graphics

---

## STEP 7: POST-PUBLICATION

### Monitor:
- Check Google Play Console for metrics
- Respond to user reviews
- Monitor crash reports
- Track downloads and ratings

### Updates:
- Increment version number in app.json
- Build new AAB file
- Upload to Play Console

---

## TROUBLESHOOTING

### Build Fails:
- Check internet connection
- Verify Expo login: `eas whoami`
- Clear cache: `npm cache clean --force`

### Upload Fails:
- Ensure file is .aab format
- Check file size (under 150MB)
- Verify package name matches

### Review Rejection:
- Read rejection email carefully
- Fix issues mentioned
- Resubmit with corrections

---

## CONTACT

If you get stuck:
- Copy exact error messages
- Check Google Play Console help
- Review this guide step by step

**Your app package name**: `com.sarveshgovardhanan.foodiq`
**Your Expo username**: `sarvesh2025`

Good luck with your app launch! 🚀
