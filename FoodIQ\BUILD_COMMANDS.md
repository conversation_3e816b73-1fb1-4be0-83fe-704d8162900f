# FoodIQ - Production Build Commands

## Quick Setup Commands

Run these commands in your FoodIQ directory:

### 1. Install EAS CLI (if not already installed)
```bash
npm install -g eas-cli
```

### 2. Login to Expo
```bash
eas login
# Use your username: sarvesh2025
```

### 3. Initialize EAS Build
```bash
eas build:configure
```

### 4. Build Production AAB for Play Store
```bash
eas build --platform android --profile production
```

## What Happens Next:

1. **Build Process**: Takes 10-30 minutes
2. **Email Notification**: You'll get an email when build completes
3. **Download**: Download the AAB file from the link in the email
4. **Upload**: Follow the Google Play Console instructions below

## Build Status Check:
```bash
# Check build status
eas build:list

# Download completed build
eas build:download [build-id]
```

Your app is configured with:
- **Package Name**: com.sarveshgovardhanan.foodiq
- **Expo Username**: sarvesh2025
- **Version**: 1.0.0
- **Version Code**: 1
