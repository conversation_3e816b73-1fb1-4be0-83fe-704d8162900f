{"version": 3, "names": ["React", "Keyboard", "Platform", "useIsKeyboardShown", "onShow", "onHide", "useEffect", "willShowSubscription", "willHideSubscription", "didShowSubscription", "didHideSubscription", "OS", "addListener", "_willShowSubscription", "_willHideSubscription", "remove", "removeListener", "_didShowSubscription", "_didHideSubscription"], "sourceRoot": "../../../src", "sources": ["utils/useIsKeyboardShown.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAA2BC,QAAQ,QAAQ,cAAc;AAM1E,eAAe,SAASC,kBAAkBA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,EAAE;EACpEL,KAAK,CAACM,SAAS,CAAC,MAAM;IACpB,IAAIC,oBAAyD;IAC7D,IAAIC,oBAAyD;IAC7D,IAAIC,mBAAwD;IAC5D,IAAIC,mBAAwD;IAE5D,IAAIR,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;MACzBJ,oBAAoB,GAAGN,QAAQ,CAACW,WAAW,CAAC,kBAAkB,EAAER,MAAM,CAAC;MACvEI,oBAAoB,GAAGP,QAAQ,CAACW,WAAW,CAAC,kBAAkB,EAAEP,MAAM,CAAC;IACzE,CAAC,MAAM;MACLI,mBAAmB,GAAGR,QAAQ,CAACW,WAAW,CAAC,iBAAiB,EAAER,MAAM,CAAC;MACrEM,mBAAmB,GAAGT,QAAQ,CAACW,WAAW,CAAC,iBAAiB,EAAEP,MAAM,CAAC;IACvE;IAEA,OAAO,MAAM;MACX,IAAIH,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;QAAA,IAAAE,qBAAA,EAAAC,qBAAA;QACzB,KAAAD,qBAAA,GAAIN,oBAAoB,cAAAM,qBAAA,eAApBA,qBAAA,CAAsBE,MAAM,EAAE;UAChCR,oBAAoB,CAACQ,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL;UACAd,QAAQ,CAACe,cAAc,CAAC,kBAAkB,EAAEZ,MAAM,CAAC;QACrD;QAEA,KAAAU,qBAAA,GAAIN,oBAAoB,cAAAM,qBAAA,eAApBA,qBAAA,CAAsBC,MAAM,EAAE;UAChCP,oBAAoB,CAACO,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL;UACAd,QAAQ,CAACe,cAAc,CAAC,kBAAkB,EAAEX,MAAM,CAAC;QACrD;MACF,CAAC,MAAM;QAAA,IAAAY,oBAAA,EAAAC,oBAAA;QACL,KAAAD,oBAAA,GAAIR,mBAAmB,cAAAQ,oBAAA,eAAnBA,oBAAA,CAAqBF,MAAM,EAAE;UAC/BN,mBAAmB,CAACM,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL;UACAd,QAAQ,CAACe,cAAc,CAAC,iBAAiB,EAAEZ,MAAM,CAAC;QACpD;QAEA,KAAAc,oBAAA,GAAIR,mBAAmB,cAAAQ,oBAAA,eAAnBA,oBAAA,CAAqBH,MAAM,EAAE;UAC/BL,mBAAmB,CAACK,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL;UACAd,QAAQ,CAACe,cAAc,CAAC,iBAAiB,EAAEX,MAAM,CAAC;QACpD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACA,MAAM,EAAED,MAAM,CAAC,CAAC;AACtB", "ignoreList": []}