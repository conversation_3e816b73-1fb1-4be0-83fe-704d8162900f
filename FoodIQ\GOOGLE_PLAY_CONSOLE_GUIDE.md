# FoodIQ - Google Play Console Setup Guide

## Step-by-Step Google Play Console Configuration

### Prerequisites
- Google Play Console developer account ($25 one-time fee)
- Production AAB file ready for upload
- All store assets created (icons, screenshots, feature graphic)
- Privacy policy hosted online

---

## 1. CREATE NEW APP

### Step 1.1: Access Google Play Console
1. Go to [Google Play Console](https://play.google.com/console)
2. Sign in with your developer account
3. Click **"Create app"** button

### Step 1.2: App Details
Fill out the basic information:

```
App name: FoodIQ
Default language: English (United States)
App or game: App
Free or paid: Free
```

### Step 1.3: Declarations
Check the required boxes:
- [ ] Developer Program Policies
- [ ] US export laws
- [ ] Content guidelines

Click **"Create app"**

---

## 2. APP CONTENT

### Step 2.1: Privacy Policy
1. Navigate to **"App content"** → **"Privacy policy"**
2. Enter your privacy policy URL: `https://yourwebsite.com/privacy-policy`
3. Click **"Save"**

**Required Privacy Policy Content:**
```
Privacy Policy for FoodIQ

1. Data Collection:
   - Camera access for ingredient scanning only
   - No personal information collected
   - No user tracking or analytics

2. Data Usage:
   - Images processed for text extraction
   - Analysis performed via secure AI services
   - Results displayed locally

3. Data Sharing:
   - No data shared with third parties
   - No advertising or marketing use

4. Contact: <EMAIL>
```

### Step 2.2: App Access
1. Go to **"App content"** → **"App access"**
2. Select **"All functionality is available without special access"**
3. Click **"Save"**

### Step 2.3: Ads
1. Navigate to **"App content"** → **"Ads"**
2. Select **"No, my app does not contain ads"**
3. Click **"Save"**

### Step 2.4: Content Rating
1. Go to **"App content"** → **"Content rating"**
2. Click **"Start questionnaire"**

**Questionnaire Answers:**
```
Category: Reference, Education, or Information
Target age group: General audience

Violence:
- Does your app contain violence? No
- Does your app contain realistic violence? No

Sexual Content:
- Does your app contain sexual content? No
- Does your app contain nudity? No

Profanity:
- Does your app contain profanity? No

Controlled Substances:
- Does your app contain alcohol references? No
- Does your app contain drug references? No
- Does your app contain tobacco references? No

Gambling:
- Does your app contain simulated gambling? No

Interactive Elements:
- Can users interact with each other? No
- Can users share information? No
- Does your app share location? No
```

3. Click **"Save"** and **"Submit"**

### Step 2.5: Target Audience
1. Navigate to **"App content"** → **"Target audience"**
2. Select age groups: **"18 and older"** (primary), **"13-17"** (secondary)
3. Click **"Save"**

### Step 2.6: News Apps (Skip)
Not applicable for FoodIQ - skip this section.

### Step 2.7: COVID-19 Contact Tracing (Skip)
Not applicable for FoodIQ - skip this section.

### Step 2.8: Data Safety
1. Go to **"App content"** → **"Data safety"**
2. Click **"Start"**

**Data Collection Answers:**
```
Does your app collect or share any of the required user data types?
- Select "No" for most categories
- For "Photos and videos": Select "Yes" (camera access)

Photos and videos:
- Is this data collected or shared? Collected
- Is this data processed ephemerally? Yes
- Is this data required or optional? Required
- Why is this user data collected? App functionality
```

3. Complete the questionnaire
4. Click **"Save"** and **"Submit"**

---

## 3. STORE LISTING

### Step 3.1: Main Store Listing
1. Navigate to **"Store presence"** → **"Main store listing"**

### Step 3.2: App Details
```
App name: FoodIQ
Short description: Scan food labels and get instant AI-powered health ratings for ingredients

Full description: [Use the full description from STORE_LISTING_CONTENT.md]
```

### Step 3.3: Graphics
Upload the following assets:

**App icon:**
- Upload your 512x512 PNG icon
- File: `store-assets/icon-512.png`

**Feature graphic:**
- Upload your 1024x500 feature graphic
- File: `store-assets/feature-graphic.png`

**Phone screenshots:**
- Upload 2-8 screenshots
- Files: `store-assets/screenshots/phone/01-home-screen.png`, etc.

**7-inch tablet screenshots (optional):**
- Upload tablet screenshots if available

**10-inch tablet screenshots (optional):**
- Upload large tablet screenshots if available

### Step 3.4: Categorization
```
App category: Health & Fitness
Tags: health, nutrition, food, ingredients, scanner, AI, wellness
```

### Step 3.5: Contact Details
```
Website: https://foodiq.app (or your website)
Email: <EMAIL> (or your email)
Phone: [Optional]
```

### Step 3.6: External Marketing (Optional)
Leave blank unless you have specific marketing URLs.

Click **"Save"**

---

## 4. RELEASE MANAGEMENT

### Step 4.1: App Signing
1. Navigate to **"Release"** → **"Setup"** → **"App signing"**
2. Choose **"Use Google-generated key"** (recommended)
3. Accept the terms
4. Click **"Save"**

### Step 4.2: Create Release
1. Go to **"Release"** → **"Production"**
2. Click **"Create new release"**

### Step 4.3: Upload App Bundle
1. Click **"Upload"** in the "App bundles" section
2. Select your AAB file: `foodiq-production.aab`
3. Wait for upload and processing

### Step 4.4: Release Details
```
Release name: 1.0.0 (auto-generated)

Release notes:
🎉 Welcome to FoodIQ v1.0!

✨ Features:
• Smart ingredient label scanning
• AI-powered health analysis
• Detailed ingredient breakdown
• Health tips and recommendations
• Healthier alternatives suggestions

📱 Perfect for making informed food choices!

🔒 Privacy-focused with secure local processing
```

### Step 4.5: Release Settings
```
Release type: Full rollout
Countries/regions: All countries (or select specific ones)
```

---

## 5. TESTING TRACKS (RECOMMENDED)

### Step 5.1: Internal Testing Track
1. Navigate to **"Release"** → **"Testing"** → **"Internal testing"**
2. Click **"Create new release"**
3. Upload your AAB file
4. Add internal testers (email addresses)
5. Set up for 1-2 weeks of testing

### Step 5.2: Closed Testing Track
1. Go to **"Release"** → **"Testing"** → **"Closed testing"**
2. Create a new track (e.g., "Alpha")
3. Upload AAB file
4. Add external testers (20-100 people)
5. Test for 2-3 weeks

### Step 5.3: Open Testing Track
1. Navigate to **"Release"** → **"Testing"** → **"Open testing"**
2. Create public beta
3. Upload AAB file
4. Set maximum testers (e.g., 1000)
5. Test for 1-2 weeks

---

## 6. REVIEW AND SUBMIT

### Step 6.1: Pre-Submission Checklist
- [ ] App content sections completed
- [ ] Store listing filled out completely
- [ ] All graphics uploaded
- [ ] Privacy policy URL added
- [ ] Content rating completed
- [ ] Data safety form submitted
- [ ] AAB file uploaded successfully
- [ ] Release notes written

### Step 6.2: Review Summary
1. Go to **"Publishing overview"**
2. Check for any warnings or errors
3. Resolve any issues highlighted

### Step 6.3: Submit for Review
1. Navigate to your production release
2. Click **"Review release"**
3. Review all information
4. Click **"Start rollout to production"**

---

## 7. REVIEW PROCESS

### Step 7.1: Review Timeline
- **Typical review time**: 1-3 days
- **Maximum review time**: 7 days
- **Holiday periods**: May take longer

### Step 7.2: Review Status
Monitor status in Google Play Console:
- **Under review**: Google is reviewing your app
- **Approved**: App is approved and will be published
- **Rejected**: Issues found, needs fixes

### Step 7.3: Common Rejection Reasons
1. **Privacy policy issues**
2. **Misleading app description**
3. **Broken functionality**
4. **Inappropriate content**
5. **Policy violations**

---

## 8. POST-APPROVAL

### Step 8.1: Publication
- App goes live within 2-3 hours after approval
- Available in selected countries/regions
- Searchable in Google Play Store

### Step 8.2: Monitoring
1. **Install metrics**: Track downloads and installs
2. **Crash reports**: Monitor app stability
3. **User reviews**: Respond to feedback
4. **Performance**: Watch key metrics

### Step 8.3: Store Listing Optimization
- Monitor search rankings
- A/B test descriptions and graphics
- Update based on user feedback
- Optimize for relevant keywords

---

## 9. TROUBLESHOOTING

### Common Issues and Solutions

#### Issue: App Bundle Upload Fails
**Solution:**
- Check file size (max 150MB)
- Verify AAB format
- Ensure proper signing
- Try uploading again

#### Issue: Privacy Policy Rejected
**Solution:**
- Ensure URL is accessible
- Include all required sections
- Match app functionality
- Use HTTPS URL

#### Issue: Content Rating Problems
**Solution:**
- Review questionnaire answers
- Ensure accuracy
- Resubmit if necessary
- Contact support if stuck

#### Issue: Store Listing Rejected
**Solution:**
- Check for policy violations
- Verify all required fields
- Ensure graphics meet specifications
- Review description for accuracy

---

## 10. ONGOING MANAGEMENT

### Regular Tasks
- **Weekly**: Check reviews and respond
- **Monthly**: Review analytics and performance
- **Quarterly**: Update app content and optimize listing
- **As needed**: Release updates and bug fixes

### Key Metrics to Monitor
- Install conversion rate
- User ratings and reviews
- Crash-free sessions percentage
- User retention rates
- Search ranking for key terms

This guide provides complete instructions for setting up your FoodIQ app in Google Play Console and successfully publishing to the Play Store.
